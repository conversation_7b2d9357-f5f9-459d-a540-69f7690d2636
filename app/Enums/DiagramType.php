<?php
namespace App\Enums;

enum DiagramType: string {
    case Bar       = 'bar';
    case Line      = 'line';
    case Radar     = 'radar';
    case PolarArea = 'polarArea';
    case Progress  = 'progress';
    case ProgressBarSmall = 'progressBarSmall';
    case Biorythm = 'biorythm';
    case Menu = 'menu';
    case OwnTopic = 'own_topic';
    case Filters = 'filters';
    case FrequencyGenerator = 'frequency_generator';
    case RichText = 'richtext';

    public function label(): string
    {
        return match ($this) {
            self::Bar => 'Bar Chart',
            self::Line => 'Line Chart',
            self::Radar => 'Radar Chart',
            self::PolarArea => 'Polar Area Chart',
            self::Progress => 'Progress Bar',
            self::ProgressBarSmall => 'Progress Bar Small',
            self::Biorythm => 'Biorhythm Chart',
            self::Menu => 'Menu',
            self::OwnTopic => 'Own Topic',
            self::Filters => 'Filters',
            self::FrequencyGenerator => 'Frequency Generator',
            self::RichText => 'Rich Text',
        };
    }

    public static function options(): array
    {
        return array_map(
            fn($type) => ['value' => $type->value, 'label' => $type->label()],
            self::cases()
        );
    }
}
