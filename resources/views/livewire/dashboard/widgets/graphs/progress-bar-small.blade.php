<div class="grid-stack-item-content" style="height: 100%; padding: 10px;">
    <div class="square-progress-container d-flex flex-column h-100">
        <div class="dashboard-progress-bar-container widget-scroll flex-grow-1" style="max-height: 100%">
            @if (!$selectedLongDay)
            @foreach ($results as $key => $item)
            <div wire:key="dashboard-small-progress-container-{{ $key }}-{{ $item['analysis_id'] }}"
                class="dashboard-progress-container progress-{{ $key }} d-flex justify-content-between align-items-center pb-2 flex-wrap">
                <!-- Title Section -->
                <div class="title-section d-flex align-items-center justify-content-start">
                    <p class="text-truncate mb-0" data-toggle="tooltip" title="{{ $item['name'] }}">
                        {{ $item['name'] }}
                    </p>
                </div>

                <!-- Actions + Progress Color Square -->
                <div class="actions-progress d-flex align-items-center justify-content-end">
                    @if (!empty($item['description']) || !empty($item['desc_img']) || !empty($item['bodyDesc']) ||
                    !empty($item['mentalDesc']))
                    <span class="info popover-trigger" data-poolid="{{ $poolId }}"
                        data-analysisid="{{ $item['analysis_id'] }}" data-key="{{ $key }}" data-container="body"
                        data-toggle="popover" data-trigger="click" data-placement="bottom" data-html="true"
                        data-content="">
                        <i class="fas fa-info-circle"></i>
                    </span>
                    @endif

                    <livewire:dashboard.single-analyses-add-to-cart
                        :wire:key="'dashboard-small-progress-btn1-'.$key . '-' .$item['analysis_id'] . '-' . $poolId"
                        :poolId="$poolId" :analysesId="$item['analysis_id']" />

                    <div class="progress-bar-container d-flex align-items-center">
                        <svg width="20" height="20" class="progress-svg">
                            <rect x="0" y="0" width="20" height="20" fill="#e0e0e0" rx="4" ry="4"></rect>
                            <rect x="0" y="0" width="20" height="20" fill="{{ $item['color'] }}" rx="4" ry="4"></rect>
                        </svg>
                        <div class="progress-value">{{ $item['val'] }}%</div>
                    </div>
                </div>
            </div>

            <!-- Popover Content -->
            <div id="popover-content-{{ $poolId }}-{{ $item['analysis_id'] }}" class="d-none">
                @if (!empty($item['description']))
                <h6>{{ __('action.main_desc') }}</h6>
                <p>{!! $item['description'] !!}</p>
                @endif
                @if (isset($item['desc_img']))
                <img loading="lazy" src="{{ asset('/storage/analyse/images/description/' . $item['desc_img']) }}"
                    class="popover-showimg" id="popover-img{{ $item['analysis_id'] }}-desc" data-type="desc"
                    data-id="{{ $item['analysis_id'] }}" alt="Description Image" height="250px" width="auto">
                @endif
                @if (!empty($item['bodyDesc']))
                <hr>
                <h6>{{ __('action.body') }}</h6>
                <p>{!! $item['bodyDesc'] !!}</p>
                @endif
                @if (!empty($item['mentalDesc']))
                <hr>
                <h6>{{ __('action.mental') }}</h6>
                <p>{!! $item['mentalDesc'] !!}</p>
                @endif
            </div>
            @endforeach

            <!-- Modal for image popup -->
            <div id="analysisPopup_Modal" class="modal analysisPopup_Modal">
                <span class="close" id="modal-close">&times;</span>
                <img class="modal-content" id="imgShow">
            </div>
            @else
            @foreach($results as $key => $item)
            @php
            $redWidth = ($item['RED'] / ($item['RED'] + $item['GREEN'])) * 100;
            $greenWidth = 100 - $redWidth;
            @endphp

            <div wire:key="dashboard-long-day-small-progress-container-{{ $key }}"
                style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px; flex-wrap: nowrap;">
                <!-- Label -->
                <div style="max-width: 60%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                    <p class="text-truncate mb-0" data-toggle="tooltip" title="{{ $item['name'] }}">
                        {{ $item['name'] }}
                    </p>
                </div>

                <!-- Right content -->
                <div
                    style="width: 100px; display: flex; align-items: center; gap: 4px; flex-shrink: 0; margin-right: 14px;">
                    <livewire:dashboard.single-analyses-add-to-cart
                        :wire:key="'dashboard-long-day-progress-container-'.$key .'-'. $item['analysis_id']"
                        :poolId="$poolId" :analysesId="$item['analysis_id']" />

                    <div
                        style="flex-grow: 1; height: 20px; position: relative; border-radius: 4px; background-color: #f3f3f3; overflow: hidden;">
                        <div
                            style="position: absolute; height: 100%; background-color: #E84E1B; width: {{ $redWidth }}%; left: 0;">
                        </div>
                        <div
                            style="position: absolute; height: 100%; background-color: #2FAB66; width: {{ $greenWidth }}%; left: {{ $redWidth }}%;">
                        </div>
                    </div>

                    <div
                        style="font-size: 14px; white-space: nowrap; display: flex; align-items: center; font-weight: bold;">
                        <span style="color: #E84E1B;" title="Rote Werte">{{ $item['RED'] }}</span>/
                        <span style="color: #2FAB66;" title="Grüne Werte">{{ $item['GREEN'] }}</span>
                    </div>
                </div>
            </div>
            @endforeach
            @endif
        </div>
    </div>

    <style>
        .dashboard-progress-container {
            gap: 10px;
        }

        .dashboard-progress-container .title-section {
            flex: 1 1 40%;
            min-width: 0;
        }

        .dashboard-progress-container .title-section p {
            margin-bottom: 0;
            min-width: 0;
        }

        .dashboard-progress-container .actions-progress {
            flex: 0 0 auto;
            gap: 10px;
        }

        .dashboard-progress-container .info i {
            font-size: 14px;
            width: 14px;
            height: 14px;
            line-height: 14px;
            display: inline-block;
            vertical-align: middle;
        }

        .dashboard-progress-container .progress-bar-container {
            gap: 4px;
            min-width: 75px;
            font-size: 12px;
            color: #333;
        }

        .dashboard-progress-container .progress-svg {
            flex-shrink: 0;
        }

        .dashboard-progress-container .progress-value {
            width: 25px;
            text-align: right;
            user-select: none;
        }

        .popover-showimg {
            max-width: 100%;
            height: auto;
            cursor: pointer;
        }

        .widget-scroll {
            overflow-y: auto;
        }

        @media screen and (max-width: 768px) {
            .dashboard-progress-container {
                flex-direction: column !important;
                align-items: flex-start;
            }

            .dashboard-progress-container>div {
                width: 100% !important;
                flex: none !important;
                margin-bottom: 8px;
            }

            .dashboard-progress-container .actions-progress {
                justify-content: flex-start !important;
                gap: 15px;
            }

            /* Ensure proper height when used with images */
            .grid-stack-item-content {
                height: auto !important;
                min-height: 200px !important;
            }

            .square-progress-container {
                height: auto !important;
                min-height: 180px !important;
            }

            .dashboard-progress-bar-container.widget-scroll {
                max-height: none !important;
                height: auto !important;
                overflow-y: visible !important;
            }
        }

        /* Additional improvements for very small mobile screens */
        @media screen and (max-width: 576px) {
            .dashboard-progress-container {
                padding: 6px !important;
                margin-bottom: 6px !important;
                font-size: 12px;
            }

            .dashboard-progress-container .title-section p {
                font-size: 12px !important;
            }

            .dashboard-progress-container .progress-bar-container {
                min-width: 60px !important;
                font-size: 11px !important;
            }

            .dashboard-progress-container .progress-value {
                width: 20px !important;
                font-size: 10px !important;
            }
        }
    </style>
</div>

@script
<script>
    const initTooltips = () => {
        $('[data-toggle="tooltip"]').tooltip({ container: 'body' });
    };

    const initPopovers = () => {
        $('[data-toggle="popover"]').each(function () {
            const $el = $(this);
            $el.popover('dispose').popover({
                html: true,
                trigger: 'manual',
                placement: 'bottom',
                container: 'body',
                content: function () {
                    const id = `#popover-content-${$el.data('poolid')}-${$el.data('analysisid')}`;
                    return $(id).html();
                }
            });

            $el.off('click').on('click', function (e) {
                e.stopPropagation();
                $('[data-toggle="popover"]').not(this).popover('hide');
                $el.popover('toggle');
            });
        });

        $(document).off('click.popover-close').on('click.popover-close', function (e) {
            if (!$(e.target).closest('.popover, [data-toggle="popover"]').length) {
                $('[data-toggle="popover"]').popover('hide');
            }
        });
    };

    const initImageModal = () => {
        $(document).off('click.image-modal').on('click.image-modal', '.popover-showimg', function () {
            $('#imgShow').attr('src', this.src);
            $('#analysisPopup_Modal').fadeIn();
        });

        $('#modal-close, #analysisPopup_Modal').off('click.image-modal').on('click.image-modal', function (e) {
            if (e.target.id === 'modal-close' || e.target.id === 'analysisPopup_Modal') {
                $('#analysisPopup_Modal').fadeOut();
            }
        });
    };

    const initAll = () => {
        initTooltips();
        initPopovers();
    };

    document.addEventListener('DOMContentLoaded', () => {
        initAll();
        initImageModal();
    });

    document.addEventListener('livewire:navigated', () => setTimeout(initAll, 100));
    document.addEventListener('livewire:updated', () => setTimeout(initAll, 100));
    $wire.on('$refresh', () => setTimeout(initAll, 100));
</script>
@endscript