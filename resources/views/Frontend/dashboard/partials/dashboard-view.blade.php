{{-- Gridstack CSS and Required Fonts --}}
<link href="https://cdn.jsdelivr.net/npm/gridstack@12.1.2/dist/gridstack.min.css" rel="stylesheet">
<link rel="preconnect" href="https://rsms.me/">
<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

<style type="text/css">
    :root {
        --main-bg-color: #ffffff;
        --main-bg-color-light-gray: #f5f5f5;
        --widget-bg-color: #ffffff;
        --text-color: #34495e;
        --text-muted-color: #7f8c8d;
        --border-color: #ecf0f1;
    }

    .dashboard-view-container {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        background-color: var(--main-bg-color-light-gray);
        color: var(--text-color);
        line-height: 1.6;
        padding: 20px;
    }

    /* Mobile padding adjustment */
    @media (max-width: 768px) {
        .dashboard-view-container {
            padding: 10px;
        }
    }

    .grid-stack {
        margin: -5px;
    }

    /* Mobile grid adjustments */
    @media (max-width: 768px) {
        .grid-stack {
            margin: 0;
            gap: 0;
        }

        /* Force single column layout on mobile */
        .grid-stack > .grid-stack-item {
            width: 100% !important;
            left: 0 !important;
            position: relative !important;
            margin-bottom: 0 !important;
            padding: 0 !important;
            top: auto !important;
            margin-top: 0 !important;
        }

        .grid-stack > .grid-stack-item[gs-w="12"] {
            width: 100% !important;
        }

        /* Remove GridStack's internal spacing */
        .grid-stack-item .grid-stack-item-content {
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            padding: 0 !important;
        }
    }

    .grid-stack-item {
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .grid-stack-item-content {
        background: transparent;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .custom-widget-container {
        width: 100%;
        height: 100%;
        background-color: var(--widget-bg-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 15px;
        font-size: 0.85rem;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        min-height: 200px; /* Ensure minimum height on mobile */
    }

    /* Mobile widget container adjustments */
    @media (max-width: 768px) {
        .custom-widget-container {
            padding: 12px;
            font-size: 0.9rem;
            border-radius: 8px;
            margin-bottom: 10px; /* Add gap between widgets */
        }

        /* Remove margin from last widget */
        .grid-stack-item:last-child .custom-widget-container {
            margin-bottom: 0;
        }
    }

    @media (max-width: 576px) {
        .custom-widget-container {
            padding: 10px;
            font-size: 0.85rem;
        }
    }

    .custom-widget-body {
        flex: 1;
        min-height: 0;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .custom-widget-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-bottom: 10px;
        /* padding-bottom: 10px; */
        min-height: 44px; /* Increased from 30px to match button height */
        border-bottom: 1px solid var(--border-color);
        flex-shrink: 0;
        flex-wrap: wrap;
        gap: 8px;
    }

    .custom-widget-header .title {
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        min-width: 0;
        margin: 0; /* Remove any default margin */
        line-height: 1.1; /* Use normal line-height instead of matching min-height */
    }

    /* Mobile header adjustments */
    @media (max-width: 576px) {
        .custom-widget-header .title {
            font-size: 1rem;
        }
    }

   .custom-widget-header .controls {
        margin-left: 10px;
        flex-shrink: 0;
        min-height: 44px; /* Ensure controls area has consistent height */
        display: flex;
        align-items: center;
    }

    /* Special containers for Biorhythm and Menu */
    .biorhythm-container,
    .menu-container {
        flex: 1;
        min-height: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    /* Mobile adjustments for special containers */
    @media (max-width: 768px) {
        .biorhythm-container,
        .menu-container {
            min-height: 250px;
        }
    }

    /* Force full height for Biorhythm and Menu contents */
    .biorhythm-container > div,
    .menu-container > div {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .pool-content__title {
        border-bottom: none !important;
    }

    /* Fixed responsive image container */
    .widget-image-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 200px;
        overflow: hidden;
        padding: 10px;
    }

    .widget-image-container img {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
        border-radius: 8px;
    }

    /* Improved responsive row layout */
    .widget-content-row {
        display: flex;
        height: 100%;
        min-height: 0;
        gap: 15px;
    }

    .widget-content-row.reverse {
        flex-direction: row-reverse;
    }

    .widget-graph-column,
    .widget-image-column {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
    }

    .widget-graph-column {
        min-height: 200px;
    }

    /* Enhanced mobile responsiveness */
    @media (max-width: 992px) {
        .widget-content-row,
        .widget-content-row.reverse {
            flex-direction: column;
            gap: 12px;
        }

        .widget-graph-column {
            min-height: 180px;
        }

        .widget-image-container {
            min-height: 180px;
            max-height: 250px;
        }
    }

    @media (max-width: 768px) {
        .widget-content-row,
        .widget-content-row.reverse {
            gap: 10px;
        }

        .widget-image-container {
            min-height: 150px;
            max-height: 200px;
            padding: 8px;
        }

        .widget-graph-column {
            min-height: 150px;
        }
    }

    /* Small screen adjustments */
    @media (max-width: 576px) {
        .widget-image-container {
            min-height: 120px;
            max-height: 150px;
            padding: 5px;
        }

        .widget-graph-column {
            min-height: 120px;
        }
    }

    .no-widgets-message {
        text-align: center;
        padding: 40px;
        color: var(--text-muted-color);
    }

    @media (max-width: 768px) {
        .no-widgets-message {
            padding: 20px;
        }

        .no-widgets-message h3 {
            font-size: 1.2rem;
        }
    }

    /* Ensure touch-friendly controls on mobile */
    @media (max-width: 768px) {
        .custom-widget-header .controls button,
        .custom-widget-header .controls a {
            min-width: 32px;
            min-height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
    }

    /* Prevent horizontal scroll on mobile */
    @media (max-width: 768px) {
        body {
            overflow-x: hidden;
        }

        .dashboard-view-container {
            overflow-x: hidden;
        }
    }
    /* Mobile adjustments */
    @media (max-width: 768px) {
        .custom-widget-header .controls button,
        .custom-widget-header .controls a {
            min-width: 44px;
            min-height: 44px;
        }
    }
</style>

<div class="dashboard-view-container dashboard-designer-mode">
    @if (!empty($dashboardData))
        <div class="grid-stack">
            @foreach ($dashboardData as $widget)
                <div class="grid-stack-item" gs-x="{{ $widget['x'] ?? 0 }}" gs-y="{{ $widget['y'] ?? 0 }}"
                     gs-w="{{ $widget['width'] ?? 4 }}" gs-h="{{ $widget['height'] ?? 2 }}" gs-id="{{ $widget['id'] }}">

                    <div class="grid-stack-item-content">
                        @php
                            $diagramType = strtolower($widget['diagram_type'] ?? '');
                        @endphp
                        @if ($diagramType === 'biorythm')
                            <div class="custom-widget-container">
                                <div class="custom-widget-body">
                                    <div class="biorhythm-container">
                                        @include('Frontend.partials.includes.layout-biorhythmus', ['widget' => $widget])
                                    </div>
                                </div>
                            </div>
                        @elseif (in_array($diagramType, ['filters','menu','own_topic','frequency_generator']))
                            <div class="custom-widget-container">
                                <div class="custom-widget-header">
                                    <h5 class="title">{{ $widget['title'] }}</h5>
                                    <div class="controls">
                                        {{-- This empty div ensures consistent spacing and height --}}
                                    </div>
                                </div>
                                <div class="custom-widget-body">
                                    @includeIf('Frontend.dashboard.partials._navigation', ['widget' => $widget, 'diagramType' => $diagramType])
                                </div>
                            </div>

                        @elseif (in_array($diagramType, ['bar', 'line', 'radar', 'polararea', 'progress','progressbarsmall']))
                            <div class="custom-widget-container">
                                <div class="custom-widget-header">
                                    <h5 class="title">{{ $widget['title'] }}</h5>
                                    <div class="controls">
                                        <livewire:dashboard.color-analyses-add-to-cart
                                                :poolId="$widget['pool_type']"
                                                :wire:key="'color-analyses-add-to-cart-' . $widget['id']"
                                        />
                                    </div>
                                </div>
                                @if (!empty($widget['settings']['image']))
                                    @php
                                        $imageMaxWidth = $widget['settings']['image_max_width'] ?? 50;
                                        $graphWidth = 100 - $imageMaxWidth;
                                    @endphp
                                    <div class="custom-widget-body">
                                        <div class="widget-content-row {{ $widget['settings']['image_position'] === 'right' ? '' : 'reverse' }}">
                                            <div class="widget-graph-column" style="flex: 0 0 {{ $graphWidth }}%; max-width: {{ $graphWidth }}%;">
                                                @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' =>
                                                $diagramType])
                                            </div>
                                            <div class="widget-image-column" style="flex: 0 0 {{ $imageMaxWidth }}%; max-width: {{ $imageMaxWidth }}%;">
                                                <div class="widget-image-container">
                                                    <img src="{{ asset('storage/images/' . $widget['settings']['image']) }}"
                                                         alt="Widget Image" loading="lazy">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <div class="custom-widget-body">
                                        @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' =>
                                        $diagramType])
                                    </div>
                                @endif
                            </div>
                        @elseif ($diagramType === 'richtext')
                            <div class="custom-widget-container">
                                <div class="custom-widget-header">
                                    <h5 class="title">{{ $widget['title'] }}</h5>
                                    <div class="controls">
                                        {{-- This empty div ensures consistent spacing and height --}}
                                    </div>
                                </div>
                                <div class="custom-widget-body">
                                    @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' =>
                                    $diagramType])
                                </div>
                            </div>
                        @else
                            <div class="custom-widget-container">
                                <div class="custom-widget-header">
                                    <h5 class="title">Unsupported Widget</h5>
                                </div>
                                <div class="custom-widget-body">
                                    Widget type ({{ $widget['diagram_type'] }}) not supported.
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="no-widgets-message">
            <h3>Bu Dashboard'da Gösterilecek Widget Bulunmuyor.</h3>
        </div>
    @endif
</div>

<script src="https://cdn.jsdelivr.net/npm/gridstack@12.1.2/dist/gridstack-all.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        //calculate verticalMargin based on the screensize
        var verticalMargin = () => {
            switch (true) {
                case window.innerHeight < 500:
                    return 0;
                case window.innerHeight < 768:
                    return 5;
                case window.innerHeight < 1024:
                    return 10;
                default:
                    return 15;
            }
        };

        const gridOptions = {
            staticGrid: true,
            animate: false,
            margin: 5,
            float: 1,
            cellHeight: 70,
            disableOneColumnMode: false, // Enable one column mode for mobile
            disableDrag: true,
            disableResize: true, // Disable resize on all devices
            column: 12,
            verticalMargin: verticalMargin()
        };

        const grid = GridStack.init(gridOptions);
        window.addEventListener('resize', function() {
            grid.verticalMargin = verticalMargin();
        });
    });
</script>