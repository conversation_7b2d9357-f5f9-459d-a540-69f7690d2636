@media (min-width: 2000px) and (max-width: 2240px) {
    .ap-disk-img {
        height: 750px !important;
        width: 750px !important;
    }
}

@media (max-width: 2560px) and (min-width: 2000px) {
    .analysis_price {
        font-size: 48px;
        bottom: 13%;
    }

    .ap-disk-img {
        height: 800px !important;
        width: 800px !important;
    }

    .audio-player-name {
        top: 531px;
        left: 50%;
        padding: 10px 20px;
        font-size: 20px;
        letter-spacing: 0;
    }

    .frablang-subcategory img {
        height: 210px;
    }
}

@media (max-width: 2020px) and (min-width: 1730px) {
    .dashboard-progress-container>.col-lg-3 {
        -ms-flex: 0 0 30%;
        flex: 0 0 30%;
        max-width: 30%;
    }

    .dashboard-progress-container>.col-lg-9 {
        -ms-flex: 0 0 70%;
        flex: 0 0 70%;
        max-width: 70%;
    }
}

@media (max-width: 1729px) and (min-width: 1440px) {
    .dashboard-progress-container>.col-lg-3 {
        -ms-flex: 0 0 35%;
        flex: 0 0 35%;
        max-width: 35%;
    }

    .dashboard-progress-container>.col-lg-9 {
        -ms-flex: 0 0 65%;
        flex: 0 0 65%;
        max-width: 65%;
    }
}

@media (min-width: 1367px) {
    .dashboard-progress-bar-container {
        max-height: 567px;
    }

    .analysis-progressbar .analysis-title {
        max-width: 420px;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .sidenav-vertical .sidenav-inner {
        width: 100% !important;
        /* max-height: 83vh; */
        background: #fff !important;
    }

    .custom_modal .modal-content {
        position: relative;
        left: -224px !important;
        top: -1px !important;
    }
}

@media (min-width: 2000px) {

    .oldImg {
        height: 173PX !important;
        width: auto !important;
    }

    .normalImg {
        height: 173PX !important;
        width: auto !important;
    }

    .childImg {
        height: 130PX !important;
        width: auto !important;
    }

    .babyImg {
        height: 100px !important;
        width: auto !important;
    }

    .widget-three-image img.baby {
        padding-top: 0;
    }

    .oldImg-default {
        height: 173PX !important;
        width: auto !important;
    }

    .normalImg-default {
        height: 173PX !important;
        width: auto !important;
    }

    .childImg-default {
        height: 130PX !important;
        width: auto !important;
    }

    .babyImg-default {
        height: 100px !important;
        width: auto !important;
    }

    .widget-three-image img.baby-default {
        padding-top: 30px;
    }

    .kindMH {
        padding-top: 0;
        margin-top: 43px;
    }

    .babyH {
        margin-top: 72px !important
    }

    .kindMH-default {
        padding-top: 15px;
    }

    .six_start_point .inputbg {
        max-width: 81px !important;
        min-width: 81px !important;
        position: absolute;
        max-height: 40px;
        top: 40px;
        line-height: 32px;
        z-index: 1;
        left: -42px;
    }

    .six_start_point {
        border-left: 70px solid transparent;
        border-right: 70px solid transparent;
        border-bottom: 90px solid #ccc;
        position: absolute;
        top: 10px;
    }

    .six_start_point:after {
        border-left: 70px solid transparent;
        border-right: 70px solid transparent;
        border-top: 90px solid #ccc;
        top: 30px;
        left: -72px;
    }

    .star-six {
        border-left: 100px solid transparent;
        border-right: 100px solid transparent;
        border-bottom: 120px solid #b1b1b1;
    }

    .star-six:after {
        border-left: 100px solid transparent;
        border-right: 100px solid transparent;
        border-top: 120px solid #b1b1b1;
        top: 39px;
        left: -101px;
    }

    .widget-input-box-three {
        position: relative;
        z-index: 1;
        top: 52px;
        width: 119px;
        height: 56px;
        left: -59px;
        padding: 5px;
        font-size: 18px;
    }

    .chakra-content {
        padding-top: 52px;
    }

    .chakra-content-list ul li.list-two {
        margin-top: -18px;
    }

    .dsh-view .analysis-sample-header-left {
        width: 85%;
    }

    #crnDraftModal .modal-dialog {
        margin: 0 auto;
    }

    .dashboard-progress-content {
        margin-top: 120px;
    }

    .main-sys-view-image {
        /* height: 85%;
        width: 85%; */
        margin: 0 auto;
        position: relative;
    }

    .widget-three-image {
        padding: 30px 0;
    }

    .widget-input-box-one,
    .widget-input-box-two-input {
        height: 90px;
        width: 200px;
    }

    .widget-input-box {
        padding-top: 55px;
    }

    .widget-input-box-two {
        height: 139px;
        width: 272px;
    }

    .infotest-content {
        top: -465px;
        height: 450px;
    }

    .right-panel-list {
        max-height: 90%;
    }

    .ap-disk-img {
        height: 650px;
        width: 650px;
    }

    .audio-player {
        width: 250px;
        height: 250px;
    }

    /* .analysis-title {
        max-width: 316px;
    } */

    .analysis-progressbar .analysis-title {
        max-width: 660px;
    }

    .systematic-view .widget-three {
        height: 522px;
    }

    .widget-three-button {
        /* top: 434px; */
        width: 97%;
    }

    .widget-two {
        min-height: 50%;
    }

    .widget-two-box-content {
        height: 160px;
    }

    .widget-two-box {
        height: 470px;
    }

    .main-sys-view {
        padding-top: 50px;
        min-height: 95%;
    }

    /* .imgdiv img {
        height: auto;
        width: 60px;
    } */
    .widget-three-image img {
        padding-bottom: 10px;
        height: 160px;
    }

    .widget-three-image input {
        height: 55px;
    }

    .focusAction_btn button {
        padding: 10px 16px !important;
        line-height: 18px;
        font-size: 20px;
    }

    .inputbg {
        height: 44px;
        max-width: 115px !important;
        min-width: 115px;
    }

    .styled-font {
        font-size: 20px;
    }

    .inputbg2>span {
        height: 42px;
    }

    .inputbg2 {
        height: 70px;
        width: 142px;
    }

    .imgdiv .popname {
        position: absolute;
        top: -60px;
        left: 47%;
        background-color: #fff;
        color: #000;
        padding: 5px 12px;
        font-size: 20px;
        border-radius: 4px;
        z-index: 1;
        transform: translateX(-50%);
        text-align: center;
        width: max-content;
    }

    .imgdiv .popname::after {
        position: absolute;
        content: "";
        height: 15px;
        width: 15px;
        background-color: #fff;
        top: 37px;
        left: 50%;
        transform: rotate(45deg) translateX(-50%);
        z-index: -9;
    }

    .main-sys-view-shape.shape-one {
        width: 220px;
        height: 220px;
        top: 60;
        left: 185;
    }

    .main-sys-view-shape.shape-two {
        width: 220px;
        height: 300px;
        top: 523px;
        left: 70;
    }

    .main-sys-view-shape.shape-three {
        width: 202px;
        height: 350px;
    }

    .main-sys-view-shape.shape-four {
        width: 300px;
        height: 220px;
    }

    .main-sys-view-shape.shape-five {
        width: 300px;
        height: 190px;
    }

    .main-sys-view-shape.shape-six {
        width: 350px;
        height: 215px;
        right: 355px;
        top: 175px;
    }

    .main-sys-view-shape.shape-seven {
        width: 176px;
        height: 237px;
        top: 528px;
    }

    .main-sys-view-shape.shape-eight {
        width: 250px;
        height: 300px;
    }

    .main-sys-view-shape.shape-nine {
        width: 297px;
        height: 92px;
        top: 255px;
        left: 205px;
    }

    .main-sys-view-shape.shape-ten {
        width: 150px;
        height: 150px;
    }

    .main-sys-view-shape.shape-eleven {
        width: 200px;
        height: 180px;
    }

    .main-sys-view-shape.shape-twelve {
        width: 270px;
        height: 278px;
        top: 335px;
        right: 50px;
    }

    .main-sys-view-shape.shape-thirteen {
        width: 215px;
        height: 250px
    }

    .main-sys-view-shape.shape-fourteen {
        width: 250px;
        height: 225px;
        right: 393px;
        top: 402px;
    }

    .main-sys-view-shape.shape-fifteen {
        width: 250px;
        height: 320px;
        top: 455px;
        left: 315px;
    }

    .main-sys-view-shape.shape-sixteen {
        width: 90px;
        height: 110px;
        top: 365px;
        left: 315px;
    }

    .main-sys-view-shape.shape-seventeen {
        width: 55px;
        height: 90px;
        top: 345px;
        right: 315px;
    }

    .main-sys-view-shape.shape-eightteen {
        width: 110px;
        height: 90px;
        top: 355px;
        left: 180px;
    }

    .main-sys-view-shape.shape-ninteen {
        width: 55px;
        height: 130px;
        bottom: 255px;
        left: 125px;
    }

    .main-sys-view-shape.shape-twenty {
        width: 120px;
        height: 90px;
        bottom: 315px;
        right: 135px;
    }

    .main-sys-view-shape.shape-tone {
        width: 90px;
        height: 90px;
        top: 255px;
        right: 215px;
    }

    .main-sys-view-shape.shape-ttwo {
        width: 90px;
        height: 70px;
        bottom: 115px;
        right: 220px;
    }

    .main-sys-view-shape.shape-tthree {
        width: 110px;
        height: 80px;
        bottom: 300px;
        left: 340px
    }

    .main-sys-view-shape.shape-tfour {
        width: 125px;
        height: 28px;
        top: 450px;
        right: 165px;
    }

    .main-sys-view-shape.shape-tfive {
        width: 175px;
        height: 90px;
        bottom: 200px;
        right: 465px;
    }

    .main-sys-view-shape.shape-tsix {
        width: 90px;
        height: 125px;
        bottom: 175px;
        left: 410px;
    }

    .main-sys-view-shape.shape-tseven {
        width: 70px;
        height: 50px;
        top: 175px;
        left: 240px;
    }

    .main-sys-view-shape.shape-teight {
        width: 80px;
        height: 80px;
        bottom: 320px;
        left: 545px;
    }

    .main-sys-view-shape.shape-tnine {
        width: 100px;
        height: 135px;
        bottom: 240px;
        right: 355px;
    }

    .main-sys-view-shape.shape-tthirty {
        width: 135px;
        height: 45px;
        bottom: 170px;
        left: 100px;
    }

    .main-sys-view-shape.shape-ttone {
        width: 110px;
        height: 90px;
        bottom: 180px;
        left: 265px;
    }

    .info-test-content-box-two .progress {
        width: 500px !important;
    }
}

@media (min-width: 1281px) {
    .focus-img-w-50 {
        max-width: 50% !important;
        flex: 50% !important;
    }
}

@media (max-width: 1440px) and (min-width: 1366px) {
    #my-div {
        height: 850px;
    }

    .dashboard-progress-container>.col-lg-3 {
        -ms-flex: 0 0 40%;
        flex: 0 0 40%;
        max-width: 40%;
    }

    .dashboard-progress-container>.col-lg-9 {
        -ms-flex: 0 0 60%;
        flex: 0 0 60%;
        max-width: 60%;
    }
}

@media (max-width: 1366px) and (min-width: 1200px) {

    /* .tip-content {left: -4px !important;} */

    /* .tip-content .arrow {
        left: calc(63% - 10px) !important;
    } */

    #my-div {
        height: 1000px;
    }

    .home a.nav-link {
        padding: 5px 10px;
    }

    .mid svg.circle-progress {
        height: 100px;
        width: 100px;
    }

    .analysis-content-body .analysis-sample-header {
        display: flex;
        justify-content: space-evenly;
    }

    /* .main-sys-view {
        padding-top: 30px;
    } */
    .main-sys-view {
        padding-bottom: 0px;
        padding-top: 0px;
    }

    .minus-btn {
        top: 0;
        right: -14px;
    }

    .dashboard-progress-container>.col-lg-3 {
        -ms-flex: 0 0 44%;
        flex: 0 0 44%;
        max-width: 44%;
    }

    .dashboard-progress-container>.col-lg-9 {
        -ms-flex: 0 0 56%;
        flex: 0 0 56%;
        max-width: 56%;
    }
}

@media (max-width: 1366px) and (min-width: 1281px) {
    .frablang-subcategory img {
        height: 130px;
    }

    .frablang-treat .preloader img {
        left: 42.5%;
        transform: translate(-50%, -50%);
    }
}

@media (max-width: 1280px) and (min-width: 1025px) {
    .analysis-progressbar-sample-list li span {
        margin-bottom: 7px;
    }

    .tip-content {
        left: -45px !important;
    }

    .tip-content .arrow {
        left: calc(76% - 10px) !important;
    }

    .right-panel-list {
        max-height: 80% !important;
    }

    .frablang-subcategory img {
        height: 115px;
    }

    .frablang-treat .preloader img {
        left: 42.2%;
        transform: translate(-50%, -50%);
    }

}

@media (max-width: 1024px) and (min-width: 768px) {
    .frablang-subcategory img {
        height: 110px;
    }

    .frablang-treat .preloader img {
        left: 40.2%;
        transform: translate(-50%, -50%);
    }

    /* iPad specific widget improvements */
    .widget-content-row {
        display: flex !important;
        align-items: stretch !important;
        height: 100% !important;
    }

    .widget-image-column {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 8px !important;
    }

    .widget-graph-column {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 8px !important;
    }

    .widget-image-container {
        width: 100% !important;
        height: 100% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        overflow: hidden !important;
    }

    .widget-image-container img {
        max-width: 100% !important;
        max-height: 100% !important;
        width: auto !important;
        height: auto !important;
        object-fit: contain !important;
        object-position: center !important;
    }

    /* Specific improvements for progress-bar-small on iPad */
    .widget-content-row .widget-graph-column .grid-stack-item-content {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .widget-content-row .widget-graph-column .square-progress-container {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .widget-content-row .widget-graph-column .dashboard-progress-bar-container {
        flex-grow: 1 !important;
        overflow-y: auto !important;
        max-height: 100% !important;
    }
}

@media (max-width: 1280px) {

    .frablang-treat-video-content button {
        height: 40px;
        width: 65px;
        border-radius: 10px;
        line-height: 35px;
    }

    .frablang-treat-video-content i {
        font-size: 15px;
    }

    .p-all-sm-0 {
        padding-left: 0;
        padding-right: 0;
    }

    .d-xl-none {
        display: block !important;
    }

    /* .dis-none{
        display: none !important;
    }   */
    .systematic-view .widget-three {
        margin-top: 30px !important;
        padding-bottom: 60px;
        /* padding: 20px 15px !important; */
    }

    .l-100-px {
        width: 100px !important;
    }

    .analysis-pointer {
        margin-top: -14px;
    }

    .userFullName {
        max-width: 250px;
        width: auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

@media (max-width: 1024px) {
    #crnDraftModal>.modal-dialog {
        padding: 0 25px !important;
    }

    .paymentWrap .paymentBtnGroup .paymentMethod {
        padding: 38px;
        height: 60px;
    }

    .right-panel-list {
        max-height: 74% !important;
    }

    .widget-two {
        margin-top: 31px;
    }

    .col-xl-7.dis-w-100 {
        max-width: 100%;
        flex: 100%;
    }

    .col-xl-5.dis-w-100 {
        max-width: 100%;
        flex: 100%;
    }

    .dashboard-card .col-md-4 {
        flex: 100% !important;
        max-width: 100% !important;
    }

    .dashboard-card .col-md-8 {
        flex: 100% !IMPORTANT;
        max-width: 100%;
    }

    .tab-content .arrow {
        left: calc(220px - 10px) !important;
        z-index: 1;
    }

    .tab-navigation .dropdown-menu.show {
        left: 24px !important;
    }

    button#autoGenerate,
    button#clearGenerate {
        width: 100%;
        margin-bottom: 10px;
    }

    .custom-select-box {
        width: 100% !important;
    }

    .card-datatable.table-responsive {
        padding-bottom: 47px !important;
    }

    #card-datatable.card-datatable.table-responsive {
        padding-bottom: 1.5rem !important;
    }

    .analysis-progressbar-sample-list li span {
        width: 100%;
    }
}

@media (max-width: 1200px) and (min-width: 768px) {
    #my-div {
        height: 1500px;
    }

    .home a.nav-link {
        padding: 5px 10px;
    }

    .card-footer button {
        margin-right: 10px;
        margin-bottom: 10px !important;
    }

    .systematic-view .widget-three {
        padding-bottom: 60px;
        /* padding: 20px 15px !important; */
    }

}

@media (max-width: 1414px) and (min-width: 1200px) {
    html .chakra-img-container {
        width: 100%;
        flex: 100%;
        max-width: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    html .cus-chakra-content {
        width: 100%;
        max-width: 100%;
        flex: 100%;
    }

    html .chakra-body.cus-card-body {
        min-width: unset;
    }

    html.layout-collapsed .chakra-img-container {
        flex: 0 0 41.66666667%;
        max-width: 41.66666667%;
        position: unset;
        transform: unset;
    }

    html.layout-collapsed .cus-chakra-content {
        max-width: 58.33333333%;
        flex: 0 0 58.33333333%;
    }

    html.layout-collapsed .chakra-body.cus-card-body {
        min-width: unset;
    }

}

@media (max-width: 1200px) {
    .chakra-img-container {
        width: 100%;
        flex: 100%;
        max-width: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .cus-chakra-content {
        width: 100% !important;
        max-width: 100% !important;
        flex: 100%;
    }

    .chakra-body.cus-card-body {
        min-width: unset;
    }

    div.dashboard-product-aria:has(> section.dash-chakra) {
        min-height: unset !important;
    }

    .cus-chakra-img {
        max-width: 60% !important;
    }
}

@media (max-width: 768px) {

    .sidenav-toggle::after {
        top: 50%;
        display: block;
        width: 8px;
        height: 8px;
    }

    /* .sidenav:not(.sidenav-no-animation) .sidenav-item .sidenav-link {
        position: relative;
        left: -3px;
    } */
    .user-tab-content {
        position: relative;
        padding: 20px 10px !important;
    }

    /* Widget image improvements for iPad and mobile */
    .widget-content-row {
        flex-direction: column !important;
        height: auto !important;
    }

    .widget-image-column,
    .widget-graph-column {
        flex: 1 1 100% !important;
        max-width: 100% !important;
        width: 100% !important;
    }

    .widget-image-column {
        order: 2;
        margin-top: 10px;
        max-height: 200px !important;
    }

    .widget-graph-column {
        order: 1;
        min-height: 200px;
    }

    .widget-image-container {
        max-height: 180px !important;
        overflow: hidden;
    }

    .widget-image-container img {
        max-height: 180px !important;
        width: auto !important;
        object-fit: contain !important;
    }

    /* Specific fixes for progress-bar-small widgets with images */
    .custom-widget-body .widget-content-row .widget-graph-column .grid-stack-item-content {
        height: auto !important;
        min-height: 200px !important;
    }

    .custom-widget-body .widget-content-row .widget-graph-column .square-progress-container {
        height: auto !important;
        min-height: 180px !important;
    }

    .custom-widget-body .widget-content-row .widget-graph-column .dashboard-progress-bar-container {
        max-height: none !important;
        height: auto !important;
    }

    /* Additional mobile improvements for progress-bar-small */
    .custom-widget-body .widget-content-row .widget-graph-column .dashboard-progress-container {
        margin-bottom: 8px !important;
        padding: 8px !important;
        border-radius: 4px;
        background: #f8f9fa;
    }

    .custom-widget-body .widget-content-row .widget-graph-column .dashboard-progress-container:last-child {
        margin-bottom: 0 !important;
    }
}

/* Additional mobile improvements for very small screens */
@media (max-width: 576px) {
    .widget-image-column {
        max-height: 150px !important;
        margin-top: 8px !important;
    }

    .widget-image-container {
        max-height: 130px !important;
        padding: 8px !important;
    }

    .widget-image-container img {
        max-height: 120px !important;
    }

    .widget-graph-column {
        min-height: 150px !important;
    }

    /* Progress bar small specific mobile improvements */
    .custom-widget-body .widget-content-row .widget-graph-column .dashboard-progress-container {
        padding: 6px !important;
        margin-bottom: 6px !important;
        font-size: 12px;
    }

    .custom-widget-body .widget-content-row .widget-graph-column .dashboard-progress-container .title-section p {
        font-size: 12px !important;
    }

    .custom-widget-body .widget-content-row .widget-graph-column .dashboard-progress-container .progress-bar-container {
        min-width: 60px !important;
        font-size: 11px !important;
    }
}

@media (max-width: 1200px) and (min-width: 769px) {
    .usb-photo img {
        width: 60px;
        height: 60px;
    }

    .usb-icon i {
        height: 60px;
        color: #d2d2d2;
        width: 60px;
        font-size: 30px;
        line-height: 60px;
    }

    .usb-content p {
        font-size: 12px;
        line-height: 16px;
    }

    .usb-content span {
        font-size: 12px;
        letter-spacing: 0;
        line-height: 16px;
    }

    .minus-btn {
        font-size: 16px;
        top: -5px;
        right: -17px;
    }
}

@media (max-width: 1200px) and (min-width: 991.92px) {
    .analysis-progressbar-sample-list li span {
        position: relative;
        display: block !important;
    }

    .cal-date {
        width: 136px;
        padding-left: 5px !important;
        padding-right: 0 !important;
    }

    .analysis-progressbar .analysis-title {
        max-width: 200px;
    }

    /* .main-sys-view-image {
        height: 80%;
        width: 80%;
    } */

    .mid svg.circle-progress {
        height: 100px;
        width: 100px;
    }

    label.lb-1 {
        width: 30% !important;
    }

    .home .tab-content .card-body,
    .products .tab-content .card-body {
        padding: 10px;
    }

    .home .tab-content .card-footer button {
        margin-bottom: 5px;
    }

    .analysis-progressbar-sample-list li {
        display: block;
        margin: 10px 0;
    }

    .analysis-progressbar-sample-list {
        margin-top: 0;
    }

    .analysis-progressbar .analysis-sample-header {
        margin-top: 38px;
    }

    .analysis-progressbar .progress {
        margin-top: 38px;
    }

    .analysis-progressbar .right-icon {
        margin-top: 20px;
    }

    .analysis-progressbar .left-icon {
        margin-top: 30px;
    }

    .chakra-content-list ul li {
        width: 44%;
    }

    .no-padding-lg {
        padding: 0 !important;
    }

    .right-panel {
        width: 260px;
    }

    .corndraft .dropdown-toggle::after {
        margin-left: 53% !important;
    }

    /* .preloader img {
        left: 30%;
    } */

    .analysis-content .analysis-sample-header-left {
        width: 72%;
    }

    /* .analysis-title {
        max-width: 122px;
    } */

    .analysis-sample .card-body {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }

    .chakra-content-list li p.chakra-list-heading {
        max-width: 100px;
    }

    .analysis-sample-header {
        padding-left: 10px;
        padding-right: 10px;
    }

    .main-sys-view {
        min-height: 810px;
    }

    .rightside-bottom-container .card-body {
        overflow-x: auto;
    }

    .dashboard-progress-container>.col-lg-3 {
        -ms-flex: unset;
        flex: unset;
        min-width: 150px;
        padding: 0;
    }

    .dashboard-progress-container>.col-lg-9 {
        -ms-flex: unset;
        flex: unset;
        min-width: 190px;
    }
}

@media (max-width: 991px) and (min-width: 768px) {
    .paymentWrap .paymentBtnGroup .paymentMethod {
        padding: 37px;
        height: 61px;
    }

    .right-panel-button>div button {
        width: 49%;
        background: #fff;
        font-size: 14px;
    }

    .right-panel-list {
        max-height: 68% !important;
    }

    .tabs-itemBox-Style {
        padding: 7px 14px;
        font-size: 12px;
    }

    .tab-navigation .custom-select-box {
        width: 156px !important;
        height: 33px !important;
    }

    .cus-chakra-img {
        max-width: 52% !important;
    }
}

@media (max-width: 991px) and (min-width: 800px) {
    .lta-progress {
        width: 65%;
        margin-top: 22px;
    }

    .lta-progress-normal {
        margin-top: -12px;
        width: 150px;
        margin-left: 20%;
    }
}

@media (max-width: 991px) {
    div.dashboard-product-aria:has(> section.dash-chakra) {
        margin-bottom: 1.5rem;
    }

    .layout-overlay.layout-sidenav-toggle {
        z-index: 111111;
    }

    #layout-sidenav {
        z-index: 1111111;
    }
}

@media (max-width: 767px) {
    .analysisPopup_Modal .close {
        top: -1px;
    }

    .erro_page .icon-section img {
        max-width: 100%;
    }

    .frablang-treat .preloader img {
        left: 38%;
        transform: translate(-50%, -50%);
    }

    #ut-input-container .col-md-6 {
        padding: 0px !important;
    }

    .password-form-group {
        padding-right: 12px !important;
    }

    .userFullName {
        max-width: 185px;
    }

    .dataTables_wrapper>.row:first-child>.col-sm-12 {
        width: 50%;
        max-width: 50%;
    }

    .dataTables_wrapper>.row:first-child .dataTables_length label {
        justify-content: flex-start;
    }

    .dataTables_wrapper>.row:first-child .dataTables_filter label {
        justify-content: flex-end;
    }

    .cus-chakra-img {
        max-width: 65% !important;
    }
}

@media (max-width: 800px) and (min-width: 768px) {
    .paymentWrap .paymentBtnGroup .paymentMethod {
        padding: 30px;
        height: 49px;
    }

    .frablang-treat .preloader img {
        left: 37.5%;
        transform: translate(-50%, -50%);
    }
}

@media (max-width: 799px) and (min-width: 575.92px) {
    .lta-progress {
        margin-top: 18px !important;
        width: 65% !important;
    }

    .lta-progress-normals {
        position: relative;
        margin-top: -2px !important;
        width: 105px !important;
        margin-left: 24% !important;
    }

}

@media (max-width: 992px) and (min-width: 575.92px) {
    .chakra-content-list li.list-one {
        margin-top: 10px;
    }

    #crnDraftModal button {
        font-size: 13px;
        line-height: 16px;
    }

    .dsh-view .analysis-sample-header-left {
        width: 74%;
    }

    /* .main-sys-view-image {
        height: 80%;
        width: 80%;
    } */


    .main-sys-view {
        min-height: 650px;
    }

    .right-panel-list {
        max-height: 84%;
    }

    label.lb-1 {
        width: 100% !important;
    }

    label.lb-100 {
        width: 100% !important;
    }

    #preview-modal .modal-content {
        width: 520px !important;
    }

    .popover .popover-body {
        width: 238px !important;
    }

    .home .tab-content .card-body {
        padding: 10px;
    }

    .home .tab-content .card-footer button {
        margin-bottom: 5px;
    }

    .navbar-nav {
        flex-direction: row;
    }

    .navbar-brand {
        margin-left: 10px;
    }

    .navbar-nav .dropdown-menu {
        position: absolute !important;
        float: none;
    }

    /* .sys-setting li {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        flex-direction: column;
    }

    .sys-setting li p {
        width: auto;
        margin-bottom: 10px;
    } */

    .home .nav-tabs-top>.tab-content,
    .products .nav-tabs-top>.tab-content {
        width: 100%;
    }

    .topic textarea {
        height: 150px;
    }

    .home .tab-content>.tab-pane {
        padding: 10px;
    }

    .progress-circle-mini {
        padding: 5px 0;
    }

    #morrisjs-donut {
        height: 200px !important;
    }

    .progress-circle-mini .morris-progressbar-mini {
        height: 120px !important;
        width: 100px !important;
        margin: 0 auto;
    }

    .analysis-content-header .logo i {
        font-size: 35px;
    }

    .analysis-content-header .heading h2 {
        font-size: 22px;
    }

    .analysis-content-right {
        /* text-align: left !important; */
    }

    .card-header.analysis-sample-header {
        padding: 10px;
    }

    /* .analysis-title {
        max-width: 100px;
    } */

    .mid svg.circle-progress {
        height: 100px;
        width: 100px;
    }

    .analysis-sample .card-body {
        padding: 20px 15px;
    }

    /* .analysis-sample-body {
        justify-content: space-between;
    } */

    /* .lta-normal-body {
        justify-content: none !important;
    } */

    .lta-progress-body {
        justify-content: none !important;
    }

    .lta-leftIcon-long {
        top: 14px;
    }

    .analysis-sample-body .left-icon span i {
        font-size: 24px;
    }

    .left-icon .male-icon i {
        font-size: 32px !important;
    }

    .analysis-progressbar-sample-list li {
        display: block;
        margin: 10px 0;
        position: relative;
    }

    .analysis-progressbar-sample-list {
        margin-top: 0;
    }

    .analysis-progressbar .analysis-sample-header {
        margin-top: 38px;
    }

    .analysis-progressbar .progress {
        margin-top: 38px;
    }

    .analysis-progressbar .right-icon {
        margin-top: 20px;
    }

    .analysis-progressbar .left-icon {
        margin-top: 30px;
    }

    .right-panel .card-header h4 {
        font-size: 14px;
    }

    .no-padding-md {
        padding: 0 !important;
    }

    .chakra-content-list ul li {
        width: 45%;
    }

    .chakra-content {
        padding-top: 50px;
    }

    label.lb-md-2 {
        width: 100% !important;
    }

    .corndraft .dropdown-toggle::after {
        margin-left: 53% !important;
    }

    /* .preloader img {
        left: 26%;
    } */

    .analysis-progressbar .analysis-title {
        max-width: 100px;
    }

    .heart-counts {
        position: absolute;
        margin-left: 26px;
        margin-top: -30px;
    }

    .male-counts {
        position: absolute;
        margin-left: 20px;
        margin-top: -40px;
    }
}

@media (max-width: 576px) and (min-width: 320px) {
    .m-mt-5 {
        margin-top: 5px;
        display: inline-block;
    }

    /* .ss_div_images img {
        height: 75px;
        width: auto;
    } */

    .frablang-treat .preloader img {
        top: 283px !important;
        left: 28.5% !important;
        transform: translate(-50%, -50%);
    }

    .frablang-subcategory img {
        height: 100px;
    }

    .frablang-m-lr-0 {
        margin: 0 !important;
    }

    .skitter .container_skitter {
        overflow: unset !important;
        position: relative;
    }

    .skitter .container_skitter .image_main {
        height: 100vh !important;
    }

    .treat .preloader img {
        top: 41.5%;
        left: 28.5%;
    }

    #analysis_name {
        max-width: 345px;
        overflow: hidden;
        white-space: normal;
    }

    #crnDraftModal>.modal-dialog {
        padding: 5px 5px !important;
    }

    .erro_page .content-section h2 {
        font-size: 14px;
    }

    .paymentWrap .paymentBtnGroup .paymentMethod {
        padding: 29px;
        box-shadow: none;
        position: relative;
        display: block;
        height: 45px;
    }

    .navbar-nav .nav-item .dashboard-option {
        padding-right: 0px !important;
    }

    .d-mobile-none {
        display: none !important;
    }

    .d-mobile-on {
        display: inline !important;
    }

    .analysis-content-right ul li {
        display: inline-table;
        margin-bottom: 10px;
    }

    .custom_modal .modal-content {
        position: relative;
        left: 0;
        top: 0;
    }

    .close-lta {
        margin-left: 8px;
    }

    .footer-left,
    .footer-right {
        text-align: center !important;
    }

    .widget-two-button button {
        padding: 8px 15px;
        font-size: 13px;
    }

    #btnScrShot b {
        display: none;
    }

    #saveUp b {
        display: none;
    }

    /* .sidenav-vertical .sidenav-inner {
        max-height: 100vh !important;
    } */
    .card-tools.pull-right {
        float: none !IMPORTANT;
        text-align: center;
        margin-top: 10px;
    }

    #star-six {
        width: 0;
        height: 0;
        border-left: 50px solid transparent;
        border-right: 50px solid transparent;
        border-bottom: 80px solid #fff;
        position: relative;
        top: -10px;
    }

    #star-six:after {
        width: 0;
        height: 0;
        border-left: 50px solid transparent;
        border-right: 50px solid transparent;
        border-top: 80px solid #fff;
        position: absolute;
        content: "";
        top: 30px;
        left: -50px;
    }

    .widget-input-box-three {
        position: relative;
        z-index: 1;
        top: 24px;
        width: 55px;
        height: 33px;
        left: -28px;
        padding: 5px;
    }

    .color-input {
        margin-left: 8%;
    }

    .layout-sidenav-toggle {
        margin-bottom: -54px
    }

    .layout-sidenav-toggle .nav-item i {
        padding: 6px 10px;
        font-size: 20px !important;
    }

    .dropdown.focus-dropdown {
        text-align: left;
    }

    .l-100-px {
        width: 100% !important;
        margin-bottom: 4px !important;
    }

    .ap-disk-img {
        height: auto;
        width: 100%;
        padding: 0 12px;
    }

    .audio-player-name {
        top: 264px;
    }

    #analysis_name {
        padding: 8px 12px;
        border-radius: 5px;
    }

    .treat-main-content {
        height: 84vh;
        /* margin-left: 30px;
        margin-top: 10px; */
        margin-left: 0px;
        margin-top: 0px;
        /* padding: 15px 20px 0px 20px; */
    }

    .treat .right-panel {
        /* margin-top: 20px;
        margin-left: 30px; */
        margin-left: 0px;
        padding: 15px 19px 0px 19px;
    }

    .form-group.form-inline.allBgImgSelectBox .dropdown.bootstrap-select {
        right: 20px;
    }

    .right-panel-list {
        max-height: 84%;
    }

    .treat-main-content-image {
        margin-left: 10px;
    }

    .analysis-progressbar-sample-list li span {
        width: 100px !important;
        margin-bottom: 4px;
    }

    .dis-h-100 {
        height: 100%;
    }

    .user input,
    #selectview,
    .user select {
        width: 100% !important;
    }

    .corn-button button {
        width: 100%;
    }

    .w-25 {
        width: 50% !important;
    }

    .p-all-sm-0 {
        padding-left: 0;
        padding-right: 0;
    }

    .widget-three-button button {
        line-height: 20px;
        font-size: 12px;
    }

    .widget-three-button {
        text-align: center;
        align-items: center;
        margin-top: -22px;
        bottom: -145px;
    }

    .widget-three-button-default {
        bottom: -150px !important;
    }

    .systematic-view .widget-three {
        /* margin-top: 140px !important; */
        padding: 20px 15px !important;
        height: 610px;
    }

    .usb-photo img {
        width: 80px;
        height: 80px;
    }

    .usb-icon i {
        height: 80px;
        color: #d2d2d2;
        width: 80px;
        font-size: 30px;
        line-height: 80px;
    }

    .home .nav-tabs .nav-link.active,
    .home .nav-tabs .nav-link {
        margin-right: 0 !important;
        font-size: 12px;
        padding: 8px 15px;
    }

    .default-style div.dataTables_wrapper div.dataTables_length label,
    .default-style div.dataTables_wrapper div.dataTables_filter label,
    .default-style div.dataTables_wrapper div.dataTables_info,
    .default-style div.dataTables_wrapper div.dataTables_paginate {
        justify-content: flex-start;
        margin-bottom: 10px !important;
    }

    .act-btn {
        font-size: 12px;
        margin-bottom: 10px;
    }

    .add-menu .tab-content {
        background-color: #cccccc2e;
    }

    label.lb-1 {
        width: 100% !important;
    }

    label.lb-100 {
        width: 100% !important;
    }

    .analysis-progressbar-sample-list {
        margin-top: 0;
    }

    .analysis-progressbar .analysis-sample-header {
        margin-top: 38px;
    }

    .analysis-progressbar .progress {
        margin-top: 38px;
    }

    .analysis-progressbar .right-icon {
        margin-top: 20px;
    }

    .analysis-progressbar .left-icon {
        margin-top: 30px;
    }

    #right-panel-slide .modal-dialog {
        width: 300px;
    }

    #saveCartModal .modal-dialog,
    #topicModal .modal-dialog,
    #openCartModal .modal-dialog {
        width: 100% !important;
    }

    .popover .popover-body {
        width: 322px !important;
    }

    .analysis-content-header .logo i {
        font-size: 35px;
    }

    .analysis-content-header .heading h2 {
        font-size: 20px;
    }

    .layout-navbar {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
    }

    .nav-left .navbar-brand {
        margin-left: 10px;
        display: none;
    }

    .nav-right {
        /* margin-top: 20px; */
        align-self: flex-end;
    }

    .nav-right .navbar-nav {
        flex-direction: row;
        align-items: flex-start;
    }

    .navbar-nav .nav-item .dashboard-option {
        padding-right: 10px;
    }

    .navbar-nav .dropdown-menu {
        position: absolute;
    }

    .dashboard-progress-content {
        margin-bottom: 40px;
    }

    /* .preloader img {
        left: 20%;
    } */

    .home .tab-content .card-body {
        padding: 10px;
    }

    .analysis-content-right ul li:last-child {
        border-right: 0;
        /* display: block; */
    }

    select#changeShowFilter {
        width: 82px;
        vertical-align: baseline;
    }

    .chakra-content-list ul li {
        width: 45%;
    }

    .chakra-content-list .dropdown-submenu .dropdown-menu,
    .chakra-content-list .dropleft .dropdown-submenu .dropdown-menu {
        display: none !important;
    }

    .chakra-content-list .dropdown-submenu:hover .dropdown-menu,
    .chakra-content-list .dropdown-submenu:focus .dropdown-menu,
    .chakra-content-list .dropleft .dropdown-submenu:hover .dropdown-menu,
    .chakra-content-list .dropleft .dropdown-submenu:focus .dropdown-menu {
        display: block !important;
    }

    .chakra-content-list .dropleft .dropdown-submenu:hover .dropdown-menu,
    .chakra-content-list .dropleft .dropdown-submenu:focus .dropdown-menu {
        left: -251px;
    }

    .chakra-content-list .dropdown-submenu .dropdown-menu::after {
        z-index: -1;
        left: 106%;
    }

    .chakra-content-list .dropleft .dropdown-submenu .dropdown-menu::after {
        top: 10px;
        left: 105%;
    }

    .chakra-content-list .dropright .dropdown-submenu:hover .dropdown-menu,
    .chakra-content-list .dropright .dropdown-submenu:focus .dropdown-menu {
        left: -235px;
    }

    .chakra-content-list .dropright .dropdown-submenu .dropdown-menu::after {
        z-index: -1;
        left: 97%;
    }

    .analysis-content-right {
        margin-top: 20px;
        text-align: left !important;
    }

    .analysis-sample-header,
    .analysis-sample .card-body {
        padding-left: 14px !important;
        padding-right: 14px !important;
    }

    .analysis-sample-body {
        justify-content: space-around;
    }

    .corn-setting .card-footer button {
        width: auto !important;
    }

    .create-corn .card-footer button {
        width: auto;
    }

    #preview-modal .modal-content {
        width: 310px !important;
    }

    #preview-modal .modal-body button {
        width: 50% !important;
        margin-bottom: 10px;
    }

    #crnDraftModal .modal-header .close,
    #crnDraftModal .modal-slide .close {
        top: 10%;
    }

    .main-sys-view {
        height: 370px;
        width: 100%;
        padding-top: 20px;
    }

    .widget-input-box-two {
        height: 75px;
        width: 80px;
    }

    .widget-input-box-one,
    .widget-input-box-two-input {
        height: 35px;
        width: 60px;
    }

    .widget-two-box-content-inner {
        padding: 5px;
    }

    .widget-two-box-content p {
        display: inline-block;
        max-width: 72px;
        word-break: break-word;
        font-size: 12px;
    }

    .info-test-content-box-two .progress {
        width: 200px !important;
    }

    #trash {
        height: 50px;
        width: 50px;
        bottom: 5px;
    }

    #trash i {
        font-size: 30px !important;
    }

    .star-six {
        width: 0;
        height: 0;
        border-left: 40px solid transparent;
        border-right: 40px solid transparent;
        border-bottom: 65px solid #b1b1b1;
        position: relative;
        top: -4px;
    }

    .star-six:after {
        width: 0;
        height: 0;
        border-left: 40px solid transparent;
        border-right: 40px solid transparent;
        border-top: 65px solid #b1b1b1;
        position: absolute;
        content: "";
        top: 18px;
        left: -42px;
    }

    .add-new-menu-button a button,
    .add-user-group-button button {
        display: block;
        width: 100%;
        margin-top: 5px;
    }

    .treat-main-content-container {
        margin: 0px;
        padding: 15px 20px 0px 20px;
    }

    .treat .custom-right-panel {
        margin: 0px;
        padding: 0px 25px 0px 25px;
    }

}

@media (max-width: 375px) {
    .paymentWrap .paymentBtnGroup .paymentMethod {
        padding: 24px;
        height: 40px;
    }

    .widget-two-button button {
        padding: 8px 10px !important;
        font-size: 12px !important;
    }

    #uploaded-logo {
        min-width: 100% !important;
        max-width: 100% !important;
    }
}

@media (max-width: 1366px) {

    #crnDraftModal>.modal-dialog {
        padding: 0 45px;
    }

    .customClss .progressBar1 {
        width: 200px;
        height: 200px;
        transition: .5s;
    }

    .customClss .progressBar2 {
        width: 140px;
        height: 140px;
        transition: .5s;
    }

    .lta-leftIcon {
        left: 6%;
    }

    .lta-leftIcon-long {
        left: 10%;
    }

    .lta-rightIcon-long {
        right: 10%;
    }

    .lta-progress {
        position: relative;
        width: 65%;
    }

    .lta-progres-normal {
        position: relative;
        margin-top: -12px;
        width: 150px;
    }

    .right-panel-list {
        max-height: 82%;
    }

    /* #preview-modal .modal-body{
        max-height: 450px;
        overflow: hidden;
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-color: #2fab66 #ddd9d9 !important;
        scrollbar-width: thin !important;
    } */
    #corn_view_modla {
        max-height: 600px !important;
    }
}

@media (min-width: 992px) {

    .customClss #btnEnfluss,
    .customClss #btnTopNine {
        font-size: 12px;
        padding: 8px;
    }
}

@media (max-width: 1280px) and (min-width: 992px) {
    .customClss .progressBar1 {
        width: 180px;
        height: 180px;
        transition: .5s;
    }
}

@media (min-width: 1900px) {
    #corn_view_modla {
        max-height: 600px !important;
    }
}

@media screen and (orientation: landscape) and (max-width: 767px) {
    #shopping-modal .modal-content {
        max-height: 380px;
        overflow: hidden;
        overflow-y: auto;
        overflow-x: hidden;
    }

    #shopping-modal .modal-content::-webkit-scrollbar {
        width: 5px;
    }

    #shopping-modal .modal-content::-webkit-scrollbar-track {
        border-radius: 10px !important;
        /* box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.17); */
        background-color: #ddd;
    }

    #shopping-modal .modal-content::-webkit-scrollbar-thumb {
        background-color: #2fab66;
        outline: 1px solid slategrey;
    }

    #right-panel-slide .modal-content {
        overflow: hidden;
        max-height: 380px;
        overflow-y: auto;
        overflow-x: hidden;
    }

    #right-panel-slide .modal-dialog {
        max-height: 500px;
        top: 5px !important;
        right: 4px;
    }
}

@media (max-width: 1010px) {
    .three_menu_align .add-new-menu-button {
        text-align: left !important;
    }

    .add-new-menu-button,
    .add-user-group-button {
        margin-top: 10px;
    }

    .m-mt-5 {
        margin-top: 5px;
    }
}

@media only screen and (max-width: 992px) {

    .corn-setting-tab-contents,
    .month-year-checkbox {
        flex-direction: column;
    }

    div#v-pills-tab.corn-setting-tab-sidebar {
        margin: 0px auto;
    }

    div#v-pills-tabContent.corn-setting-tab-content {
        width: 100%;
    }

    .center-action-btns {
        align-items: center;
    }

    #shareUser,
    #pdf-view-table td p,
    #package-list-table td a,
    #package-list-table td p,
    #user-group-table td a {
        margin: auto auto 2px auto;
    }

    #user-group-table td a.staff-view-btn {
        width: 100%;
        max-width: unset !important;
        text-align: center;
    }

    #package-list-table td:nth-child(5),
    #cron-recycle-bin td:nth-child(9) {
        text-align: center;
    }

    #cron-recycle-bin td:nth-child(9),
    #cron_table td:nth-child(14) {
        padding-right: 10px;
    }

    #cron_table td div.d-flex {
        justify-content: center;
    }

    .hide-table-btn-text {
        display: none;
    }

    .userFullName {
        max-width: 195px;
    }
}

/* chakra responsive */
@media only screen and (max-width: 720px) {
    #custom-top-padding>section.chakra>.row.mt-2 {
        border-bottom: none;
    }

    .chakra .col-md-12.chakra-contents-container {
        width: auto;
    }

    .chakra .chakra-contents-container .card {
        margin-bottom: 0px;
        padding-bottom: 33px;
        overflow-x: auto;
    }

    .chakra .chakra-contents-container .chakra-body .chakra-img img {
        height: 100%;
    }

    .chakra .chakra-contents-container .chakra-content-list {
        margin-bottom: 20px;
    }
}

@media only screen and (max-width: 576px) {
    div#v-pills-tab.corn-setting-tab-sidebar {
        width: 100%;
    }

    .userFullName {
        max-width: 160px;
    }

    .user-select-box {
        margin-bottom: 0px !important;
    }

    .dataTables_wrapper>.row:first-child>.col-sm-12 {
        width: 100%;
        max-width: 100%;
    }

    .dataTables_wrapper>.row:first-child .dataTables_length label,
    .dataTables_wrapper>.row:first-child .dataTables_filter label {
        justify-content: center;
    }

    .dataTables_wrapper>.row:first-child .dataTables_filter {
        padding-top: 0px !important;
    }

    .cus-chakra-img {
        max-width: 85% !important;
    }
}


@media only screen and (max-width: 1080px) {
    .userFullName {
        max-width: 210px;
    }
}

@media only screen and (max-width: 840px) {
    .userFullName {
        max-width: 340px;
    }
}

@media only screen and (max-width: 470px) {
    .userFullName {
        max-width: 130px;
    }

    #group_list+.select2-container.select2-container--default {
        width: 250px !important;
    }

    .cus-chakra-content .dropright.show .dropdown-menu.show {
        left: -145px;
    }
}

@media only screen and (max-width:410px) {
    .navbar-nav .nav-item .dashboard-option i {
        border: 0px;
        padding: 0px;
    }

    #group_list+.select2-container.select2-container--default {
        width: 200px !important;
    }
}

@media only screen and (max-width:391px) {

    .elfuless_select_box_parent,
    .elfuless_btn_box {
        max-width: 100%;
        width: 100%;
        flex: 100%;
        padding-right: 0.75rem !important;
        padding-left: 0.75rem !important;
    }

    .elfuless_btn_box {
        margin-bottom: 1rem;
    }
}